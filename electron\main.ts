import { app, BrowserWindow, screen, shell, ipcMain } from "electron"
import path from "path"
import { initializeIpcHandlers } from "./ipcHandlers"
import { ProcessingHelper } from "./ProcessingHelper"
import { ScreenshotHelper } from "./ScreenshotHelper"
import { ShortcutsHelper } from "./shortcuts"
import { initAutoUpdater } from "./autoUpdater"
import * as dotenv from "dotenv"
import { NativeShortcuts } from "./nativeShortcuts"

// Constants
const isDev = process.env.NODE_ENV === "development" || !app.isPackaged

// Application State
const state = {
  // Window management properties
  mainWindow: null as BrowserWindow | null,
  isWindowVisible: false,
  windowPosition: null as { x: number; y: number } | null,
  windowSize: null as { width: number; height: number } | null,
  screenWidth: 0,
  screenHeight: 0,
  step: 0,
  currentX: 0,
  currentY: 0,

  // Application helpers
  screenshotHelper: null as ScreenshotHelper | null,
  shortcutsHelper: null as ShortcutsHelper | null,
  processingHelper: null as ProcessingHelper | null,
  nativeShortcuts: null as NativeShortcuts | null,

  // View and state management
  view: "queue" as "queue" | "solutions" | "debug",
  problemInfo: null as any,
  hasDebugged: false,

  // Processing events
  PROCESSING_EVENTS: {
    UNAUTHORIZED: "processing-unauthorized",
    NO_SCREENSHOTS: "processing-no-screenshots",
    OUT_OF_CREDITS: "out-of-credits",
    API_KEY_INVALID: "processing-api-key-invalid",
    INITIAL_START: "initial-start",
    PROBLEM_EXTRACTED: "problem-extracted",
    SOLUTION_SUCCESS: "solution-success",
    INITIAL_SOLUTION_ERROR: "solution-error",
    DEBUG_START: "debug-start",
    DEBUG_SUCCESS: "debug-success",
    DEBUG_ERROR: "debug-error"
  } as const
}

// Add interfaces for helper classes
export interface IProcessingHelperDeps {
  getScreenshotHelper: () => ScreenshotHelper | null
  getMainWindow: () => BrowserWindow | null
  getView: () => "queue" | "solutions" | "debug"
  setView: (view: "queue" | "solutions" | "debug") => void
  getProblemInfo: () => any
  setProblemInfo: (info: any) => void
  getScreenshotQueue: () => string[]
  getExtraScreenshotQueue: () => string[]
  clearQueues: () => void
  takeScreenshot: () => Promise<string>
  getImagePreview: (filepath: string) => Promise<string>
  deleteScreenshot: (
    path: string
  ) => Promise<{ success: boolean; error?: string }>
  setHasDebugged: (hasDebugged: boolean) => void
  getHasDebugged: () => boolean
  PROCESSING_EVENTS: typeof state.PROCESSING_EVENTS
}

export interface IShortcutsHelperDeps {
  getMainWindow: () => BrowserWindow | null
  takeScreenshot: () => Promise<string>
  getImagePreview: (filepath: string) => Promise<string>
  processingHelper: ProcessingHelper | null
  clearQueues: () => void
  setView: (view: "queue" | "solutions" | "debug") => void
  isVisible: () => boolean
  toggleMainWindow: () => void
  moveInterfaceUp: () => void
  moveInterfaceDown: () => void
  moveInterfaceLeft: () => void
  moveInterfaceRight: () => void
}

export interface IIpcHandlerDeps {
  getMainWindow: () => BrowserWindow | null
  setWindowDimensions: (width: number, height: number) => void
  getScreenshotQueue: () => string[]
  getExtraScreenshotQueue: () => string[]
  deleteScreenshot: (
    path: string
  ) => Promise<{ success: boolean; error?: string }>
  getImagePreview: (filepath: string) => Promise<string>
  processingHelper: ProcessingHelper | null
  PROCESSING_EVENTS: typeof state.PROCESSING_EVENTS
  takeScreenshot: () => Promise<string>
  getView: () => "queue" | "solutions" | "debug"
  toggleMainWindow: () => void
  clearQueues: () => void
  setView: (view: "queue" | "solutions" | "debug") => void
  setHasDebugged: (value: boolean) => void
  moveInterfaceUp: () => void
  moveInterfaceDown: () => void
  moveInterfaceLeft: () => void
  moveInterfaceRight: () => void
}

// Initialize helpers
function initializeHelpers() {
  state.screenshotHelper = new ScreenshotHelper(state.view)
  state.processingHelper = new ProcessingHelper({
    getScreenshotHelper,
    getMainWindow,
    getView,
    setView,
    getProblemInfo,
    setProblemInfo,
    getScreenshotQueue,
    getExtraScreenshotQueue,
    clearQueues,
    takeScreenshot,
    getImagePreview,
    deleteScreenshot,
    setHasDebugged,
    getHasDebugged,
    PROCESSING_EVENTS: state.PROCESSING_EVENTS
  } as IProcessingHelperDeps)
  
  state.nativeShortcuts = new NativeShortcuts({
    getMainWindow,
    takeScreenshot,
    getImagePreview,
    processingHelper: state.processingHelper,
    clearQueues,
    completeAppReset,
    setView,
    getView,
    isVisible: () => state.isWindowVisible,
    toggleMainWindow,
    moveInterfaceUp,
    moveInterfaceDown,
    moveInterfaceLeft,
    moveInterfaceRight,
    quitApp: () => app.quit()
  })
}

// Window management functions
async function createWindow(): Promise<void> {
  if (state.mainWindow) {
    if (state.mainWindow.isMinimized()) state.mainWindow.restore()
    state.mainWindow.focus()
    return
  }

  const primaryDisplay = screen.getPrimaryDisplay()
  const workArea = primaryDisplay.workAreaSize
  state.screenWidth = workArea.width
  state.screenHeight = workArea.height
  state.step = 60
  state.currentY = 50

  const windowSettings: Electron.BrowserWindowConstructorOptions = {
    height: 600,
    width: 400,
    x: state.currentX,
    y: 50,
    alwaysOnTop: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: isDev
        ? path.join(__dirname, "../dist-electron/preload.js")
        : path.join(__dirname, "preload.js"),
      scrollBounce: true
    },
    show: true,
    frame: false,
    transparent: true,
    fullscreenable: false,
    hasShadow: false,
    backgroundColor: "#00000000",
    focusable: false,
    skipTaskbar: true,
    type: "panel",
    paintWhenInitiallyHidden: true,
    titleBarStyle: "hidden",
    enableLargerThanScreen: true,
    movable: false,
    acceptFirstMouse: false,
    thickFrame: false
  }

  state.mainWindow = new BrowserWindow(windowSettings)

  // Make window completely click-through - use workaround for Windows
  // The forward: true option has known issues on Windows, so we use a different approach
  state.mainWindow.setIgnoreMouseEvents(true)

  // Set additional properties for overlay behavior
  state.mainWindow.setAlwaysOnTop(true)
  state.mainWindow.setSkipTaskbar(true)

  // Workaround for Windows click-through issues
  if (process.platform === 'win32') {
    // Use a different approach for Windows
    state.mainWindow.setIgnoreMouseEvents(true)

    // Try to make it truly click-through by removing focus capability
    state.mainWindow.blur()
    state.mainWindow.setFocusable(false)
  } else {
    // Use the standard approach for other platforms
    state.mainWindow.setIgnoreMouseEvents(true, { forward: true })
  }

  // Force click-through behavior after window is ready
  state.mainWindow.once('ready-to-show', () => {
    if (state.mainWindow) {
      if (process.platform === 'win32') {
        state.mainWindow.setIgnoreMouseEvents(true)
        state.mainWindow.blur()
      } else {
        state.mainWindow.setIgnoreMouseEvents(true, { forward: true })
      }
      console.log('Window ready - click-through enabled for', process.platform)
    }
  })

  // Log for debugging
  console.log('Window created with click-through enabled for', process.platform)

  // Add more detailed logging for window events
  state.mainWindow.webContents.on("did-finish-load", () => {
    console.log("Window finished loading")
  })
  state.mainWindow.webContents.on(
    "did-fail-load",
    async (event, errorCode, errorDescription) => {
      console.error("Window failed to load:", errorCode, errorDescription)
      // Always try to load the built files on failure
      console.log("Attempting to load built files...")
      setTimeout(() => {
        state.mainWindow?.loadFile(path.join(__dirname, "../dist/index.html")).catch((error) => {
          console.error("Failed to load built files on retry:", error)
        })
      }, 1000)
    }
  )

  // Load the app - always load from built files
  console.log("Loading application from built files...")
  state.mainWindow?.loadFile(path.join(__dirname, "../dist/index.html")).catch((error) => {
    console.error("Failed to load built files:", error)
  })

  // Configure window behavior
  state.mainWindow.webContents.setZoomFactor(1)
  if (isDev) {
    state.mainWindow.webContents.openDevTools()
  }
  state.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    // Allow opening URLs in external browser
    shell.openExternal(url)
    return { action: "deny" }
  })

  // Enhanced screen capture resistance
  state.mainWindow.setContentProtection(true)

  state.mainWindow.setVisibleOnAllWorkspaces(true, {
    visibleOnFullScreen: true
  })
  state.mainWindow.setAlwaysOnTop(true, "screen-saver", 1)

  // Additional screen capture resistance settings
  if (process.platform === "darwin") {
    // Prevent window from being captured in screenshots
    state.mainWindow.setHiddenInMissionControl(true)
    state.mainWindow.setWindowButtonVisibility(false)
    state.mainWindow.setBackgroundColor("#00000000")

    // Prevent window from being included in window switcher
    state.mainWindow.setSkipTaskbar(true)

    // Disable window shadow
    state.mainWindow.setHasShadow(false)
  }

  // Prevent the window from being captured by screen recording
  state.mainWindow.webContents.setBackgroundThrottling(false)
  state.mainWindow.webContents.setFrameRate(60)

  // Set up window listeners
  state.mainWindow.on("move", handleWindowMove)
  state.mainWindow.on("resize", handleWindowResize)
  state.mainWindow.on("closed", handleWindowClosed)

  // Initialize window state
  const bounds = state.mainWindow.getBounds()
  state.windowPosition = { x: bounds.x, y: bounds.y }
  state.windowSize = { width: bounds.width, height: bounds.height }
  state.currentX = bounds.x
  state.currentY = bounds.y
  state.isWindowVisible = true
}

function handleWindowMove(): void {
  if (!state.mainWindow) return
  const bounds = state.mainWindow.getBounds()
  state.windowPosition = { x: bounds.x, y: bounds.y }
  state.currentX = bounds.x
  state.currentY = bounds.y
}

function handleWindowResize(): void {
  if (!state.mainWindow) return
  const bounds = state.mainWindow.getBounds()
  state.windowSize = { width: bounds.width, height: bounds.height }
}

function handleWindowClosed(): void {
  state.mainWindow = null
  state.isWindowVisible = false
  state.windowPosition = null
  state.windowSize = null
}

// Window visibility functions
function hideMainWindow(): void {
  if (!state.mainWindow?.isDestroyed()) {
    const bounds = state.mainWindow.getBounds()
    state.windowPosition = { x: bounds.x, y: bounds.y }
    state.windowSize = { width: bounds.width, height: bounds.height }
    state.mainWindow.setIgnoreMouseEvents(true, { forward: true })
    state.mainWindow.setAlwaysOnTop(true, "screen-saver", 1)
    state.mainWindow.setVisibleOnAllWorkspaces(true, {
      visibleOnFullScreen: true
    })
    state.mainWindow.setOpacity(0)
    state.mainWindow.hide()
    state.isWindowVisible = false
  }
}

function showMainWindow(): void {
  if (!state.mainWindow?.isDestroyed()) {
    if (state.windowPosition && state.windowSize) {
      state.mainWindow.setBounds({
        ...state.windowPosition,
        ...state.windowSize
      })
    }
    state.mainWindow.setIgnoreMouseEvents(false)
    state.mainWindow.setAlwaysOnTop(true, "screen-saver", 1)
    state.mainWindow.setVisibleOnAllWorkspaces(true, {
      visibleOnFullScreen: true
    })
    state.mainWindow.setContentProtection(true)
    state.mainWindow.setOpacity(0)
    state.mainWindow.showInactive()
    state.mainWindow.setOpacity(1)
    state.isWindowVisible = true
  }
}

function toggleMainWindow(): void {
  state.isWindowVisible ? hideMainWindow() : showMainWindow()
}

function moveInterfaceUp(): void {
  if (!state.mainWindow) return

  // Move the interface content up by moving the window down
  moveWindowVertical((y) => y + 50)
}

function moveInterfaceDown(): void {
  if (!state.mainWindow) return

  // Move the interface content down by moving the window up
  moveWindowVertical((y) => y - 50)
}

function moveInterfaceLeft(): void {
  if (!state.mainWindow) return

  // Move the interface content left by moving the window right
  moveWindowHorizontal((x) => x + 50)
}

function moveInterfaceRight(): void {
  if (!state.mainWindow) return

  // Move the interface content right by moving the window left
  moveWindowHorizontal((x) => x - 50)
}

function toggleClickThrough(): void {
  if (!state.mainWindow) return

  // Toggle click-through behavior for testing
  // Since we can't check the current state, we'll just toggle it
  state.mainWindow.setIgnoreMouseEvents(false) // Disable first
  setTimeout(() => {
    if (state.mainWindow) {
      state.mainWindow.setIgnoreMouseEvents(true, { forward: true })
      console.log('Click-through re-enabled')
    }
  }, 100)
}

// Window movement functions
function moveWindowHorizontal(updateFn: (x: number) => number): void {
  if (!state.mainWindow) return
  state.currentX = updateFn(state.currentX)
  state.mainWindow.setPosition(
    Math.round(state.currentX),
    Math.round(state.currentY)
  )
}

function moveWindowVertical(updateFn: (y: number) => number): void {
  if (!state.mainWindow) return

  const newY = updateFn(state.currentY)
  // Allow window to go 2/3 off screen in either direction
  const maxUpLimit = (-(state.windowSize?.height || 0) * 2) / 3
  const maxDownLimit =
    state.screenHeight + ((state.windowSize?.height || 0) * 2) / 3

  // Log the current state and limits
  console.log({
    newY,
    maxUpLimit,
    maxDownLimit,
    screenHeight: state.screenHeight,
    windowHeight: state.windowSize?.height,
    currentY: state.currentY
  })

  // Only update if within bounds
  if (newY >= maxUpLimit && newY <= maxDownLimit) {
    state.currentY = newY
    state.mainWindow.setPosition(
      Math.round(state.currentX),
      Math.round(state.currentY)
    )
  }
}

// Window dimension functions
function setWindowDimensions(width: number, height: number): void {
  if (!state.mainWindow?.isDestroyed()) {
    const [currentX, currentY] = state.mainWindow.getPosition()
    const primaryDisplay = screen.getPrimaryDisplay()
    const workArea = primaryDisplay.workAreaSize
    const maxWidth = Math.floor(workArea.width * 0.5)

    state.mainWindow.setBounds({
      x: Math.min(currentX, workArea.width - maxWidth),
      y: currentY,
      width: Math.min(width + 32, maxWidth),
      height: Math.ceil(height)
    })
  }
}

// Environment setup
function loadEnvVariables() {
  try {
    // In development, load from .env file
    if (isDev) {
      dotenv.config()
    } else {
      // In production, try multiple paths for .env file
      const possiblePaths = [
        path.join(__dirname, '../.env'),           // Relative to dist-electron
        path.join(process.resourcesPath, '.env'),  // In app resources
        path.join(process.cwd(), '.env'),          // Current working directory
        path.join(__dirname, '../../.env')         // One level up from dist-electron
      ]
      
      let envLoaded = false
      for (const envPath of possiblePaths) {
        try {
          dotenv.config({ path: envPath })
          if (process.env.OPEN_AI_API_KEY) {
            console.log(`Environment loaded from: ${envPath}`)
            envLoaded = true
            break
          }
        } catch (err) {
          // Continue to next path
        }
      }
      
      // If .env file loading failed, try to get from system environment
      if (!envLoaded) {
        console.log("Could not load .env file, checking system environment variables...")
        // The API key should already be available from system environment if set
      }
    }
    
    console.log("Environment variables loaded:", {
      NODE_ENV: process.env.NODE_ENV,
      OPEN_AI_API_KEY: process.env.OPEN_AI_API_KEY ? "exists" : "missing",
      isDev: isDev,
      resourcesPath: process.resourcesPath,
      cwd: process.cwd(),
      __dirname: __dirname
    })
    
    // Check if API key is available
    if (!process.env.OPEN_AI_API_KEY) {
      console.error("❌ OPEN_AI_API_KEY not found!")
      console.error("Please ensure the .env file is present and contains OPEN_AI_API_KEY=your_key_here")
      console.error("Or set the environment variable: set OPEN_AI_API_KEY=your_key_here")
    } else {
      console.log("✅ OPEN_AI_API_KEY loaded successfully")
    }
  } catch (error) {
    console.error("Error loading environment variables:", error)
  }
}

// Initialize application
async function initializeApp() {
  try {
    loadEnvVariables()
    initializeHelpers()
    initializeIpcHandlers({
      getMainWindow,
      setWindowDimensions,
      getScreenshotQueue,
      getExtraScreenshotQueue,
      deleteScreenshot,
      getImagePreview,
      processingHelper: state.processingHelper,
      PROCESSING_EVENTS: state.PROCESSING_EVENTS,
      takeScreenshot,
      getView,
      toggleMainWindow,
      clearQueues,
      setView,
      setHasDebugged,
      moveInterfaceUp,
      moveInterfaceDown,
      moveInterfaceLeft,
      moveInterfaceRight
    })
    await createWindow()
    
    // Register stealth shortcuts
    state.nativeShortcuts?.registerShortcuts()

    initAutoUpdater()
    console.log("Auto-updater initialized in", isDev ? "development" : "production", "mode")
  } catch (error) {
    console.error("Failed to initialize application:", error)
    app.quit()
  }
}

// State getter/setter functions
function getMainWindow(): BrowserWindow | null {
  return state.mainWindow
}

function getView(): "queue" | "solutions" | "debug" {
  return state.view
}

function setView(view: "queue" | "solutions" | "debug"): void {
  state.view = view
  state.screenshotHelper?.setView(view)
}

function getScreenshotHelper(): ScreenshotHelper | null {
  return state.screenshotHelper
}

function getProblemInfo(): any {
  return state.problemInfo
}

function setProblemInfo(problemInfo: any): void {
  state.problemInfo = problemInfo
}

function getScreenshotQueue(): string[] {
  return state.screenshotHelper?.getScreenshotQueue() || []
}

function getExtraScreenshotQueue(): string[] {
  return state.screenshotHelper?.getExtraScreenshotQueue() || []
}

function clearQueues(): void {
  state.screenshotHelper?.clearQueues()
  state.problemInfo = null
  setView("queue")
}

async function completeAppReset(): Promise<void> {
  console.log("Performing complete app reset...")
  
  // Cancel all ongoing processing
  state.processingHelper?.cancelOngoingRequests()
  
  // Clear all queues and state
  state.screenshotHelper?.clearQueues()
  state.problemInfo = null
  state.hasDebugged = false
  
  // Reset view to queue
  setView("queue")
  
  // Clear React Query cache
  if (state.mainWindow && !state.mainWindow.isDestroyed()) {
    try {
      await state.mainWindow.webContents.executeJavaScript(`
        if (window.electronAPI && window.electronAPI.clearReactQueryCache) {
          window.electronAPI.clearReactQueryCache()
        }
      `)
    } catch (error) {
      console.error("Error clearing React Query cache:", error)
    }
  }
  
  // Force window refresh by hiding and showing
  if (state.mainWindow && !state.mainWindow.isDestroyed()) {
    hideMainWindow()
    setTimeout(() => {
      showMainWindow()
    }, 200)
  }
  
  console.log("Complete app reset finished")
}

async function takeScreenshot(): Promise<string> {
  if (!state.mainWindow) throw new Error("No main window available")
  return (
    state.screenshotHelper?.takeScreenshot(
      () => hideMainWindow(),
      () => showMainWindow()
    ) || ""
  )
}

async function getImagePreview(filepath: string): Promise<string> {
  return state.screenshotHelper?.getImagePreview(filepath) || ""
}

async function deleteScreenshot(
  path: string
): Promise<{ success: boolean; error?: string }> {
  return (
    state.screenshotHelper?.deleteScreenshot(path) || {
      success: false,
      error: "Screenshot helper not initialized"
    }
  )
}

function setHasDebugged(value: boolean): void {
  state.hasDebugged = value
}

function getHasDebugged(): boolean {
  return state.hasDebugged
}

// Export state and functions for other modules
export {
  state,
  createWindow,
  hideMainWindow,
  showMainWindow,
  toggleMainWindow,
  setWindowDimensions,
  moveWindowHorizontal,
  moveWindowVertical,
  getMainWindow,
  getView,
  setView,
  getScreenshotHelper,
  getProblemInfo,
  setProblemInfo,
  getScreenshotQueue,
  getExtraScreenshotQueue,
  clearQueues,
  takeScreenshot,
  getImagePreview,
  deleteScreenshot,
  setHasDebugged,
  getHasDebugged
}

app.whenReady().then(initializeApp)

app.on("will-quit", () => {
  state.nativeShortcuts?.destroy()
})
