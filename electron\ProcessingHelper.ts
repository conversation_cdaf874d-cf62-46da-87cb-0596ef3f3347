

// ProcessingHelper.ts
import fs from "node:fs";
import { ScreenshotHelper } from "./ScreenshotHelper";
import { IProcessingHelperDeps } from "./main";
import { app } from "electron";
import { BrowserWindow } from "electron";

// Import the new OpenAI SDK and Zod for schema validation
import OpenA<PERSON> from "openai";
import { z } from "zod";
import { zodResponseFormat } from "openai/helpers/zod";

const isDev = !app.isPackaged;

export class ProcessingHelper {
  private deps: IProcessingHelperDeps;
  private screenshotHelper: ScreenshotHelper;

  // AbortControllers for API requests
  private currentProcessingAbortController: AbortController | null = null;
  private currentExtraProcessingAbortController: AbortController | null = null;

  constructor(deps: IProcessingHelperDeps) {
    this.deps = deps;
    this.screenshotHelper = deps.getScreenshotHelper()!;
  }

  private async getLanguage(): Promise<string> {
    return "python";
  }

  public async processScreenshots(): Promise<void> {
    const mainWindow = this.deps.getMainWindow();
    if (!mainWindow) return;
    
    const view = this.deps.getView();
    console.log("Processing screenshots in view:", view);

    if (view === "queue") {
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.INITIAL_START);
      const screenshotQueue = this.screenshotHelper.getScreenshotQueue();
      console.log("Processing main queue screenshots:", screenshotQueue);
      if (screenshotQueue.length === 0) {
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }

      try {
        this.currentProcessingAbortController = new AbortController();
        const { signal } = this.currentProcessingAbortController;

        const screenshots = await Promise.all(
          screenshotQueue.map(async (path) => ({
            path,
            preview: await this.screenshotHelper.getImagePreview(path),
            data: fs.readFileSync(path).toString("base64"),
          }))
        );

        const result = await this.processScreenshotsWithOpenAI(screenshots, signal);

        if (!result.success) {
          console.log("Processing failed:", result.error);
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            result.error
          );
          console.log("Resetting view to queue due to error");
          this.deps.setView("queue");
          return;
        }
        
        console.log("Setting view to solutions after successful processing");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
          result.data
        );
        this.deps.setView("solutions");
      } catch (error: any) {
        console.error("Processing error:", error);
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
          error.message || "An unexpected error occurred."
        );
        console.log("Resetting view to queue due to error");
        this.deps.setView("queue");
      } finally {
        this.currentProcessingAbortController = null;
      }
    } else {
      // view == 'solutions'
      const extraScreenshotQueue = this.screenshotHelper.getExtraScreenshotQueue();
      console.log("Processing extra queue screenshots:", extraScreenshotQueue);
      if (extraScreenshotQueue.length === 0) {
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.DEBUG_START);

      this.currentExtraProcessingAbortController = new AbortController();
      const { signal } = this.currentExtraProcessingAbortController;

      try {
        const result = { success: true, data: "Extra screenshots processed" };

        if (result.success) {
          this.deps.setHasDebugged(true);
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_SUCCESS,
            result.data
          );
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            "Failed to process extra screenshots"
          );
        }
      } catch (error: any) {
         if (error.name === 'AbortError') {
            mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.DEBUG_ERROR, "Extra processing was canceled by the user.");
        } else {
            mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.DEBUG_ERROR, error.message);
        }
      } finally {
        this.currentExtraProcessingAbortController = null;
      }
    }
  }

  private async processScreenshotsWithOpenAI(
    screenshots: Array<{ path: string; preview: string; data: string }>,
    signal: AbortSignal
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const mainWindow = this.deps.getMainWindow();
      if (!mainWindow) {
        return { success: false, error: "Main window not available" };
      }

      // Check for OpenAI API key
      const apiKey = process.env.OPEN_AI_API_KEY;
      if (!apiKey) {
        return { success: false, error: "OpenAI API key not found in environment variables" };
      }

      const client = new OpenAI({
        apiKey: apiKey,
      });

      // Define a schema for the solution
      const SolutionSchema = z.object({
        solution: z.string().describe("The complete solution to the problem shown in the screenshots."),
        explanation: z.string().describe("A detailed explanation of how the solution works and why it's correct."),
        problem_description: z.string().describe("A brief description of what the problem is asking for."),
      });

      console.log("Analyzing screenshots and generating solution...");

      const solutionResponse = await client.chat.completions.create(
        {
          model: "gpt-4o",
          messages: [
            {
              role: "user",
              content: [
                {
                  type: "text",
                  text: "Look at these screenshots and solve the problem shown. This could be any type of problem - mathematical, logical, analytical, or any other kind of problem. Provide a complete solution with a detailed explanation of how it works and why it's correct. If there are multiple problems, solve the main one or the most prominent one.",
                },
                ...screenshots.map((s) => ({
                  type: "image_url" as const,
                  image_url: {
                    url: `data:image/png;base64,${s.data}`,
                    detail: 'high' as const,
                  },
                })),
              ],
            },
          ],
          response_format: zodResponseFormat(SolutionSchema, "solution_data"),
        },
        { signal }
      );

      // Parse the structured response
      let solutionData;
      try {
        const content = solutionResponse.choices[0].message.content;
        if (content) {
          solutionData = JSON.parse(content);
        } else {
          throw new Error("No content in response");
        }
      } catch (error) {
        // Fallback parsing
        const content = solutionResponse.choices[0].message.content || "";
        solutionData = {
          solution: content,
          explanation: "AI-generated solution from screenshot analysis",
          problem_description: "Problem analyzed from screenshot"
        };
      }

      return { success: true, data: solutionData };
    } catch (error: any) {
      console.error("OpenAI API Error:", error.message);

      // Handle specific OpenAI errors
      if (error.status === 429) {
        return {
          success: false,
          error: "OpenAI API rate limit exceeded. Please try again in a moment."
        };
      }

      if (error.status === 401) {
        return {
          success: false,
          error: "OpenAI API key is invalid. Please check your API key."
        };
      }

      if (error.name === "AbortError") {
        return { success: false, error: "Processing was canceled by the user." };
      }

      return {
        success: false,
        error: error.message || "Failed to process screenshots with OpenAI API.",
      };
    }
  }

  public cancelOngoingRequests(): void {
    let wasCancelled = false;

    if (this.currentProcessingAbortController) {
      this.currentProcessingAbortController.abort();
      this.currentProcessingAbortController = null;
      wasCancelled = true;
    }

    if (this.currentExtraProcessingAbortController) {
      this.currentExtraProcessingAbortController.abort();
      this.currentExtraProcessingAbortController = null;
      wasCancelled = true;
    }

    this.deps.setHasDebugged(false);
    this.deps.setProblemInfo(null);

    const mainWindow = this.deps.getMainWindow();
    if (wasCancelled && mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
    }
  }

  public cancelProcessing(): void {
    if (this.currentProcessingAbortController) {
      this.currentProcessingAbortController.abort();
      this.currentProcessingAbortController = null;
    }

    if (this.currentExtraProcessingAbortController) {
      this.currentExtraProcessingAbortController.abort();
      this.currentExtraProcessingAbortController = null;
    }
  }
}