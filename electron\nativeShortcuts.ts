import { GlobalKeyboardListener } from "node-global-key-listener"

interface NativeShortcutsDeps {
  getMainWindow: () => any
  takeScreenshot: () => void
  getImagePreview: (filepath: string) => Promise<string>
  processingHelper: any
  clearQueues: () => void
  completeAppReset: () => Promise<void>
  setView: (view: "queue" | "solutions" | "debug") => void
  getView: () => "queue" | "solutions" | "debug"
  isVisible: () => boolean
  toggleMainWindow: () => void
  moveInterfaceUp: () => void
  moveInterfaceDown: () => void
  moveInterfaceLeft: () => void
  moveInterfaceRight: () => void
  quitApp: () => void
}

export class NativeShortcuts {
  private listener: GlobalKeyboardListener
  private keyStates: Map<string, boolean> = new Map()
  private lastKeyTime: Map<string, number> = new Map()
  private sequenceBuffer: string[] = []
  private readonly SEQUENCE_TIMEOUT = 1000 // 1 second timeout for key sequences
  private mousePositionTrigger: NodeJS.Timeout | null = null
  private lastCornerTrigger: Map<string, number> = new Map()
  private readonly CORNER_COOLDOWN = 2000 // 2 second cooldown between corner triggers

  constructor(private deps: NativeShortcutsDeps) {
    console.log("Initializing stealth shortcuts...")
    try {
      this.listener = new GlobalKeyboardListener({
        windows: {
          onError: (errorCode: any) => {
            // Suppress error logging to reduce detectability
            if (process.env.NODE_ENV === 'development') {
              console.error("Windows error:", errorCode)
            }
          },
          onInfo: (info: any) => {
            // Suppress info logging to reduce detectability
            if (process.env.NODE_ENV === 'development') {
              console.log("Windows info:", info)
            }
          },
        },
      })
      console.log("Stealth keyboard listener initialized")
    } catch (error) {
      console.error("Failed to initialize stealth shortcuts:", error)
      throw error
    }
  }

  registerShortcuts() {
    console.log("Registering stealth shortcuts...")

    this.listener.addListener((e: any, down: any) => {
      this.handleKeyEvent(e)
    })

    // Start mouse position monitoring as backup trigger
    this.startMousePositionMonitoring()

    console.log("Stealth shortcuts registered")
  }

  private startMousePositionMonitoring() {
    // Monitor mouse position for edge triggers (backup method)
    this.mousePositionTrigger = setInterval(() => {
      const mainWindow = this.deps.getMainWindow()
      if (!mainWindow || mainWindow.isDestroyed()) return

      try {
        const { screen } = require('electron')
        const point = screen.getCursorScreenPoint()
        const display = screen.getDisplayNearestPoint(point)

        // Define edge trigger zones (210 pixel wide areas from each edge)
        const edgeSize = 210
        const { width, height } = display.bounds
        const currentTime = Date.now()

        // Left edge: Screenshot (or reset if coming from solutions view)
        if (point.x <= edgeSize) {
          if (this.canTriggerCorner('leftEdge', currentTime)) {
            this.executeAction('screenshot-or-reset')
            this.lastCornerTrigger.set('leftEdge', currentTime)
          }
        }

        // Right edge: Process
        if (point.x >= width - edgeSize) {
          if (this.canTriggerCorner('rightEdge', currentTime)) {
            this.executeAction('process')
            this.lastCornerTrigger.set('rightEdge', currentTime)
          }
        }

        // Top edge: Reset
        if (point.y <= edgeSize) {
          if (this.canTriggerCorner('topEdge', currentTime)) {
            this.executeAction('reset')
            this.lastCornerTrigger.set('topEdge', currentTime)
          }
        }

        // Bottom edge: Toggle
        if (point.y >= height - edgeSize) {
          if (this.canTriggerCorner('bottomEdge', currentTime)) {
            this.executeAction('toggle')
            this.lastCornerTrigger.set('bottomEdge', currentTime)
          }
        }
      } catch (error) {
        // Silently handle errors to avoid detection
      }
    }, 500) // Check every 500ms
  }

  private handleKeyEvent(e: any) {
    if (!e.rawKey) return

    const keyName = e.rawKey.name || e.rawKey.standardName
    const currentTime = Date.now()

    // Debug key names in development (only for Caps Lock)
    if (process.env.NODE_ENV === 'development' && keyName.includes('CAPSLOCK')) {
      console.log(`🔑 Key detected: "${keyName}" (state: ${e.state})`)
    }

    // Track key states
    if (e.state === "DOWN") {
      this.keyStates.set(keyName, true)
      this.lastKeyTime.set(keyName, currentTime)

      // Add to sequence buffer for pattern detection
      this.sequenceBuffer.push(keyName)
      if (this.sequenceBuffer.length > 10) {
        this.sequenceBuffer.shift() // Keep only last 10 keys
      }

      // Clean old entries from sequence buffer
      this.cleanSequenceBuffer(currentTime)

      // Check for stealth patterns instead of obvious Ctrl combinations
      this.checkStealthPatterns()

    } else if (e.state === "UP") {
      this.keyStates.set(keyName, false)
    }
  }

  private cleanSequenceBuffer(currentTime: number) {
    // Remove keys older than timeout
    this.sequenceBuffer = this.sequenceBuffer.filter((_, index) => {
      return currentTime - (this.lastKeyTime.get(this.sequenceBuffer[index]) || 0) < this.SEQUENCE_TIMEOUT
    })
  }

  private checkStealthPatterns() {
    // Use less obvious key combinations that look like normal typing

    // Pattern 1: Quick double-tap of F15 (rarely used key) for screenshot
    if (this.isKeySequence(['F15', 'F15'])) {
      this.executeAction('screenshot')
      this.clearSequenceBuffer()
    }

    // Pattern 2: F14 + F15 for processing
    if (this.isKeySequence(['F14', 'F15'])) {
      this.executeAction('process')
      this.clearSequenceBuffer()
    }

    // Pattern 3: F13 + F14 for reset
    if (this.isKeySequence(['F13', 'F14'])) {
      this.executeAction('reset')
      this.clearSequenceBuffer()
    }

    // Pattern 4: F12 + F15 for toggle
    if (this.isKeySequence(['F12', 'F15'])) {
      this.executeAction('toggle')
      this.clearSequenceBuffer()
    }

    // Pattern 5: F11 + F12 for emergency reset
    if (this.isKeySequence(['F11', 'F12'])) {
      this.executeAction('emergency-reset')
      this.clearSequenceBuffer()
    }

    // Pattern 6: Arrow keys move interface up/down (looks like normal navigation)
    if (this.isKeyPressed('UP')) {
      this.executeAction('interface-up')
    }
    if (this.isKeyPressed('DOWN')) {
      this.executeAction('interface-down')
    }
    if (this.isKeyPressed('LEFT')) {
      this.executeAction('interface-left')
    }
    if (this.isKeyPressed('RIGHT')) {
      this.executeAction('interface-right')
    }
  }

  private isKeySequence(sequence: string[]): boolean {
    if (this.sequenceBuffer.length < sequence.length) return false

    const recentKeys = this.sequenceBuffer.slice(-sequence.length)
    return sequence.every((key, index) => recentKeys[index] === key)
  }

  private isKeyHeld(keyName: string): boolean {
    return this.keyStates.get(keyName) === true
  }

  private isKeyPressed(keyName: string): boolean {
    const currentTime = Date.now()
    const lastTime = this.lastKeyTime.get(keyName) || 0
    return this.keyStates.get(keyName) === true && (currentTime - lastTime) < 500
  }

  private clearSequenceBuffer() {
    this.sequenceBuffer = []
  }

  private canTriggerCorner(corner: string, currentTime: number): boolean {
    const lastTrigger = this.lastCornerTrigger.get(corner) || 0
    return currentTime - lastTrigger > this.CORNER_COOLDOWN
  }

  private executeAction(action: string) {
    // Add small random delay to make it less detectable
    const delay = Math.random() * 50 + 10 // 10-60ms delay

    setTimeout(() => {
      switch (action) {
        case 'screenshot':
          if (process.env.NODE_ENV === 'development') {
            console.log("Stealth screenshot triggered")
          }
          this.deps.takeScreenshot()
          break
        case 'screenshot-or-reset':
          const currentView = this.deps.getView()
          if (currentView === 'solutions') {
            // Coming from solutions view - reset and take new screenshot
            if (process.env.NODE_ENV === 'development') {
              console.log("Left edge from solutions - resetting and taking screenshot")
            }
            // Clear queues and reset view
            this.deps.clearQueues()
            this.deps.setView("queue")
            // Clear React Query cache to ensure UI resets
            const mainWindow = this.deps.getMainWindow()
            if (mainWindow && !mainWindow.isDestroyed()) {
              mainWindow.webContents.executeJavaScript(`
                if (window.electronAPI && window.electronAPI.clearReactQueryCache) {
                  window.electronAPI.clearReactQueryCache()
                }
              `).catch(console.error)
            }
            // Small delay to ensure view change completes
            setTimeout(() => {
              this.deps.takeScreenshot()
            }, 200)
          } else {
            // In queue view - just take screenshot
            if (process.env.NODE_ENV === 'development') {
              console.log("Left edge from queue - taking screenshot")
            }
            this.deps.takeScreenshot()
          }
          break
        case 'process':
          if (process.env.NODE_ENV === 'development') {
            console.log("Stealth processing triggered")
          }
          this.deps.processingHelper?.processScreenshots()
          break
        case 'reset':
          if (process.env.NODE_ENV === 'development') {
            console.log("Stealth reset triggered - performing complete app reset")
          }
          // Complete app reset
          this.deps.completeAppReset().catch(console.error)
          break
        case 'emergency-reset':
          if (process.env.NODE_ENV === 'development') {
            console.log("Emergency reset triggered - performing complete app reset")
          }
          // Emergency complete app reset
          this.deps.completeAppReset().catch(console.error)
          break
        case 'toggle':
          if (process.env.NODE_ENV === 'development') {
            console.log("Stealth toggle triggered")
          }
          this.deps.toggleMainWindow()
          break
        case 'interface-up':
          if (process.env.NODE_ENV === 'development') {
            console.log("Stealth interface up triggered")
          }
          this.deps.moveInterfaceUp()
          break
        case 'interface-down':
          if (process.env.NODE_ENV === 'development') {
            console.log("Stealth interface down triggered")
          }
          this.deps.moveInterfaceDown()
          break
        case 'interface-left':
          if (process.env.NODE_ENV === 'development') {
            console.log("Stealth interface left triggered")
          }
          this.deps.moveInterfaceLeft()
          break
        case 'interface-right':
          if (process.env.NODE_ENV === 'development') {
            console.log("Stealth interface right triggered")
          }
          this.deps.moveInterfaceRight()
          break
      }
    }, delay)
  }

  destroy() {
    this.listener.kill()
    if (this.mousePositionTrigger) {
      clearInterval(this.mousePositionTrigger)
      this.mousePositionTrigger = null
    }
  }
}


