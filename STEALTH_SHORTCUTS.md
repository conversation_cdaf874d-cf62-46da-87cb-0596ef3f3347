# Stealth Shortcuts Guide

This application uses stealth shortcuts that are designed to be undetectable by keyboard monitoring tools commonly used in coding interviews.

## How It Works

Instead of using obvious key combinations like `Ctrl+H`, the app uses:
1. **Function key sequences** - Uses rarely-monitored F13-F15 keys
2. **Caps Lock combinations** - Uses Caps Lock + Arrow keys for movement
3. **Random delays** - Adds small random delays to mimic human behavior
4. **Minimal logging** - Reduces console output in production mode

## Stealth Shortcuts

### Screenshot Capture
- **F15 + F15** (double-tap F15 quickly)
- Takes a screenshot of your current screen

### Process Screenshots (Get Solution)
- **F14 + F15** (press F14, then F15)
- Sends screenshots to AI for solution generation

### Reset/Clear
- **F13 + F14** (press F13, then F14)
- Clears all screenshots and resets the app

### Toggle Window Visibility
- **F12 + F15** (press F12, then F15)
- Hides/shows the application window

### Interface Navigation
- **Arrow Keys** (single press)
  - **Up Arrow** - Move interface content up (reveals more text below)
  - **Down Arrow** - Move interface content down (reveals more text above)
  - **Left Arrow** - Move interface content left
  - **Right Arrow** - Move interface content right
- Looks like normal navigation behavior
- No modifier keys needed

## Mouse Edge Triggers (Backup Method)

If keyboard shortcuts are being monitored, you can use mouse edge triggers:

### Edge Positions (20px trigger zones)

- **Left Edge** - Take screenshot
- **Right Edge** - Process screenshots
- **Top Edge** - Reset/clear
- **Bottom Edge** - Toggle window

### How to Use Edge Triggers

1. Move mouse cursor to the very edge of your screen (within 5 pixels)
2. Hold position for a moment
3. The action will trigger automatically
4. Move mouse away from edge

### Edge Trigger Tips

- Works on any monitor in multi-monitor setups
- Completely undetectable by keyboard monitors
- Natural mouse movement that looks like normal usage
- Can be done while typing or during normal computer use
- **2-second cooldown** between triggers to prevent accidental activation
- Move mouse to screen edge (within 5 pixels) and hold briefly

## Tips for Stealth Usage

1. **Practice the sequences** before your interview
2. **Use natural timing** - don't rush the key combinations
3. **F13-F15 keys** are located at the top of most keyboards
4. **Caps Lock + Arrows** looks like normal text editing
5. **Keep the window small** and positioned discretely

## Fallback Options

If stealth shortcuts don't work on your system:

- Check if your keyboard has F13-F15 keys
- Try using an external keyboard with function keys
- The app will still work with mouse clicks on the interface

## Detection Resistance

These shortcuts are designed to:

- Avoid common monitored key combinations (Ctrl+, Alt+, Win+)
- Use keys that are rarely logged by interview monitoring software
- Mimic natural typing patterns with random delays
- Minimize system-level hooks that can be detected

## Important Notes

- Test these shortcuts before your interview
- Some keyboards may not have F13-F15 keys
- Caps Lock combinations work on all keyboards
- The app window should be positioned where it won't be visible in screen recordings
