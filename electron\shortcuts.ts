import { globalShortcut, app } from "electron"
import { IShortcutsHelperDeps } from "./main"

export class ShortcutsHelper {
  private deps: IShortcutsHelperDeps

  constructor(deps: IShortcutsHelperDeps) {
    this.deps = deps
  }

  public registerGlobalShortcuts(): void {
    console.log("Registering global shortcuts...")

    const registered = globalShortcut.register("CommandOrControl+H", async () => {
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow) {
        console.log("Ctrl+H pressed - Taking screenshot...")
        try {
          const screenshotPath = await this.deps.takeScreenshot()
          const preview = await this.deps.getImagePreview(screenshotPath)
          mainWindow.webContents.send("screenshot-taken", {
            path: screenshotPath,
            preview
          })
        } catch (error) {
          console.error("Error capturing screenshot:", error)
        }
      }
    })

    if (!registered) {
      console.error("Failed to register Ctrl+H shortcut")
    } else {
      console.log("Ctrl+H shortcut registered successfully")
    }

    const enterRegistered = globalShortcut.register("CommandOrControl+Enter", async () => {
      console.log("Ctrl+Enter pressed - Processing screenshots...")
      await this.deps.processingHelper?.processScreenshots()
    })

    if (!enterRegistered) {
      console.error("Failed to register Ctrl+Enter shortcut")
    } else {
      console.log("Ctrl+Enter shortcut registered successfully")
    }

    const resetRegistered = globalShortcut.register("CommandOrControl+R", () => {
      console.log("Ctrl+R pressed - Canceling requests and resetting queues...")

      // Cancel ongoing API requests
      this.deps.processingHelper?.cancelOngoingRequests()

      // Clear both screenshot queues
      this.deps.clearQueues()

      console.log("Cleared queues.")

      // Update the view state to 'queue'
      this.deps.setView("queue")

      // Notify renderer process to switch view to 'queue'
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send("reset-view")
        mainWindow.webContents.send("reset")
      }
    })

    if (!resetRegistered) {
      console.error("Failed to register Ctrl+R shortcut")
    } else {
      console.log("Ctrl+R shortcut registered successfully")
    }

    // Interface movement shortcuts
    const leftRegistered = globalShortcut.register("CommandOrControl+Left", () => {
      console.log("Ctrl+Left pressed - Moving interface left")
      this.deps.moveInterfaceLeft()
    })

    const rightRegistered = globalShortcut.register("CommandOrControl+Right", () => {
      console.log("Ctrl+Right pressed - Moving interface right")
      this.deps.moveInterfaceRight()
    })

    const downRegistered = globalShortcut.register("CommandOrControl+Down", () => {
      console.log("Ctrl+Down pressed - Moving interface down")
      this.deps.moveInterfaceDown()
    })

    const upRegistered = globalShortcut.register("CommandOrControl+Up", () => {
      console.log("Ctrl+Up pressed - Moving interface up")
      this.deps.moveInterfaceUp()
    })

    const toggleRegistered = globalShortcut.register("CommandOrControl+B", () => {
      console.log("Ctrl+B pressed - Toggling window visibility")
      this.deps.toggleMainWindow()
    })

    // Log registration results
    console.log("Shortcut registration results:", {
      left: leftRegistered,
      right: rightRegistered,
      down: downRegistered,
      up: upRegistered,
      toggle: toggleRegistered
    })

    console.log("All global shortcuts registered")

    // Unregister shortcuts when quitting
    app.on("will-quit", () => {
      console.log("Unregistering all global shortcuts")
      globalShortcut.unregisterAll()
    })
  }
}
